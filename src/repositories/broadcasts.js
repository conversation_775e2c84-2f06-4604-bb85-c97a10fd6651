import { FirestoreRef } from 'FirebaseRef';

export const getBroadcastLogsByContactId = async (messageId, contactId, messageType) => {

    const logs = await FirestoreRef.collection('shotx-cron')
        .doc(messageId).collection('logs')
        .where('contactId', '==', `${contactId}`)
        .orderBy('createdAt', 'asc')
        .get().then(snapshot => {
            const logs = []
            snapshot.forEach(doc => {
                logs.push(doc.data())
            })
            return logs
        })

    console.log('LOGS TYPE', messageType)
    if (messageType !== 'sniper') {
        const filteredData = {};

        for (const key in logs) {
            const seen = new Map();

            for (const item of logs[key]) {
                const existing = seen.get(item.contactId);
                if (!existing || item.createdAt < existing.createdAt) {
                    seen.set(item.contactId, item);
                }
            }

            filteredData[key] = Array.from(seen.values());
        }
        console.log('LOGS FILTRED', filteredData)
        return filteredData
    }
    console.log('LOGS', logs)
    return logs
}

export const getBroadcastsLastLogAddedByContactId = async (messageId, contactId) => {
    return await FirestoreRef.collection('shotx-cron')
        .doc(messageId).collection('logs')
        .where('contactId', '==', `${contactId}`)
        .orderBy('createdAt', 'desc')
        .limit(1)
        .get().then(snapshot => {
            const logs = []
            snapshot.forEach(doc => {
                logs.push(doc.data())
            })
            return logs
        })
}
